<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>开始抽取</title>
    <link rel="stylesheet" href="/static/layui/css/layui.css" media="all">
    <!-- 引入本地Layui JS -->
    <script src="/static/layui/layui.js"></script>
    <style>
        body,
        html {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            touch-action: none;
            -webkit-overflow-scrolling: touch;
        }

        body {
            width: 100vw;
            height: 100vh;
            background: url('/static/img/index1/背景.jpg') no-repeat center center;
            background-size: cover;
            background-attachment: fixed;
            position: relative;
            transition: background-image 0.8s ease-in-out;
            cursor: pointer;
        }

        /* 全屏状态下的背景修复 */
        body:-webkit-full-screen {
            background-attachment: scroll;
        }

        body:-moz-full-screen {
            background-attachment: scroll;
        }

        body:fullscreen {
            background-attachment: scroll;
        }

        /* 第一页样式 */
        .page1 {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            transition: opacity 0.8s ease-in-out;
        }

        .title-img {
            max-width: 600px;
            width: 60vw;
        }



        /* 第2页样式 - 根据页面2.jpg设计 */
        .page2 {
            display: none;
            height: 100vh;
            position: relative;
            transition: opacity 0.8s ease-in-out;
        }

        .page2.active {
            display: block;
        }

        /* 第2页主标题 - 按设计图调整 */
        .page2 .logo {
            position: absolute;
            top: 5vh;
            left: 12%;
            transform: translateX(-50%);
            z-index: 10;
        }

        /* 第2页主标题 - 按设计图调整 */
        .page2 .main-title {
            position: absolute;
            top: 5vh;
            left: 52%;
            transform: translateX(-50%);
            /* max-width: 600px;
            width: 55vw; */
            z-index: 10;
        }

        .start-button-container {
            position: absolute;
            top: 77%;
            left: 18.5%;
            width: 621px;
            height: 136px;
        }

        .start-button-container .my-btn {
            width: 621px;
            height: 129px;
            font-size: 80px;
            background-image: linear-gradient(to bottom, #ff4500, #e91f15, #bc1127);
            border-radius: 64px;
            color: #ffead4;
            font-weight: bold;

            border: 3px solid #ffb669;
            box-shadow: 0px 15px #b65c55;

            cursor: pointer;
            transition: all 0.3s ease
        }

        .my-btn:hover {
            transform: scale(1.1);
        }

        .page2 .timer-item .button-items {
            display: flex;
            padding-top: 10px;
            justify-content: center;
        }

        .start-button-container1 {
            padding-left: 20px;
            padding-right: 20px;
        }

        .page2 .timer-item .button-items .start-button-container1 .my-btn1 {
            border-radius: 5px;
            height: 80px;
            font-size: 33px;
            width: 140px;
            background-image: linear-gradient(to bottom, #ffebd5, #ffca92, #fe9624);
            color: #83342f;
            font-weight: bold;
            box-shadow: 0px 5px #ed9d67;
            cursor: pointer;
            transition: all 0.3s ease
        }

        .my-btn1:hover {
            transform: scale(1.1);
        }

        .page2 .timer-item .button-items .start-button-container1 .my-btn2 {
            border-radius: 5px;
            height: 80px;
            width: 235px;
            font-size: 33px;
            background-image: linear-gradient(to bottom, #ffebd5, #ffca92, #fe9624);
            color: #83342f;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0px 5px #ed9d67;
            transition: all 0.3s ease
        }

        .my-btn2:hover {
            transform: scale(1.1);
        }

        .my-btn3 {
            position: absolute;
            border-radius: 45px;
            height: 80px;
            font-size: 38px;
            width: 317px;
            background-image: linear-gradient(to bottom right, #ff7a00, #ff5b00, #d20000, #c10000);
            color: #fffdfd;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease
        }

        .my-btn3:hover {
            transform: scale(1.1);
        }

        .page2 .raffle-title {
            position: absolute;
            top: 26.5%;
            left: 0.5%;
        }

        .page2 .epicycle-title {
            position: absolute;
            top: 32.5%;
            left: 22.5%;
        }

        .page2 .next-roud-title {
            position: absolute;
            top: 32.5%;
            left: 72%;
        }

        .page2 .people-info {
            position: absolute;
            top: 42%;
            left: 2.2%;
            width: 495px;
            height: 490px;
            border: 2px solid #ea3b27;
            border-radius: 15px;
            box-shadow: inset 0 0 60px #ffd5d8;
        }

        .page2 .title {
            justify-content: center;
            align-items: center;
            display: flex;
            position: absolute;
            top: 42%;
            left: 18.3%;
            width: 752px;
            height: 490px;
            border: 2px solid #ea3b27;
            border-radius: 15px;
            box-shadow: inset 0 0 60px #ffd5d8;
        }

        .page2 .title .result-title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 90%;
            font-size: 66px;
            font-weight: bold;
            color: #2c3e50;
        }

        .spicial-text {
            color: #e3223b;
        }

        .this-result-title {
            font-size: 66px;
            font-weight: bold;
            line-height: 1.2;
            height: 155px;
            width: 89%;
            display: flex;
            justify-content: center;
        }

        .timer-item {
            position: absolute;
            top: 42%;
            left: 42.5%;
            width: 699px;
            height: 490px;
            border: 2px solid #ea3b27;
            border-radius: 15px;
            box-shadow: inset 0 0 60px #ffd5d8;
        }

        .next-turn-item {
            position: absolute;
            top: 42%;
            left: 66%;
            width: 1021px;
            height: 490px;
            border: 2px solid #8baed6;
            border-radius: 15px;
            box-shadow: inset 0px 30px 18px -11px #b9d3f1;
            background-color: #e7f0fa;
        }

        .timer-title {
            padding-top: 15px;
            font-size: 42px;
            text-align: center;
        }

        .result-show {
            font-size: 60px;
        }

        .first-show {
            padding-top: 150px;
        }

        .next-result-item {
            font-weight: bold;
            font-size: 66px;
            display: flex;
            justify-content: center;
        }

        .next-result-people {
            border-radius: 25px;
            margin-top: 30px;
            margin-right: 48px;
            margin-left: 48px;
            color: #ffffff;
            display: flex;
            justify-content: center;
            background-color: #4675b0;
            font-weight: 500;
        }

        .next-item {
            padding-left: 20px;
            padding-right: 20px;
        }

        .next-result-title {
            width: 90%;
            font-size: 66px;
            padding-top: 30px;
            padding-left: 48px;
            font-weight: bold;
            height: 220px;
            line-height: 1.2;
        }

        /* 中间背景容器 - 继续往下移动 */
        .result-container {
            position: absolute;
            top: 40vh;
            left: 50%;
            transform: translateX(-50%);
            width: 900px;
            height: 500px;
            background: url('/static/img/index3/中间背景.png') no-repeat center center;
            background-size: contain;
            z-index: 5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 抽取结果显示区域 - 按设计图调整 */
        .result-content {
            width: 700px;
            height: 350px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            gap: 30px;
            padding-left: 80px;
            padding-top: 40px;
        }

        /* 结果项样式 - 按设计图调整 */
        .result-item {
            padding-left: 20px;
            display: flex;
            padding-top: 50px;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            font-size: 48px;
            font-weight: bold;
            color: #2c3e50;
        }

        /* 滚动状态下的闪烁效果 - 只应用于文字值部分 */
        .result-show.rolling .next-title {
            animation: rolling-flash 0.2s infinite alternate;
        }

        /* 滚动状态下的闪烁效果 - 只应用于文字值部分 */
        .result-show .rolling .next-item {
            animation: rolling-flash 0.2s infinite alternate;
        }

        @keyframes rolling-flash {
            0% {
                color: #2c3e50;
                transform: scale(1);
            }

            100% {
                color: #dc3545;
                transform: scale(1.02);
            }
        }

        .result-icon {
            width: 80px;
            height: 80px;
            margin-right: 10px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            flex-shrink: 0;
        }

        .result-label {
            font-size: 41px;
            font-weight: normal;
            color: #34495e;
            min-width: 120px;
            flex-shrink: 0;
        }

        .result-text {
            text-align: left;
            line-height: 1.2;
            font-weight: 500;
        }

        .result-text1 {
            font-size: 55px;
            font-weight: bold;
            text-align: left;
            line-height: 1.2;
        }

        /* 图标样式 */
        .icon-name {
            background-image: url('/static/img/index2/姓名图标.png');
        }

        .icon-department {
            background-image: url('/static/img/index2/部门图标.png');
        }

        .icon-position {
            background-image: url('/static/img/index2/职位图标.png');
        }

        /* 倒计时容器 */
        .countdown-timer {
            padding-top: 20px;
            justify-content: center;
            gap: 20px;
            z-index: 15;
        }

        /* 确保倒计时器可见 */
        .countdown-timer[style*="flex"] {
            display: flex !important;
        }

        /* 倒计时显示容器 */
        .countdown-display {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* 分钟和秒钟背景容器 */
        .time-unit {
            width: 250px;
            height: 250px;
            background: url('/static/img/index2/倒计时背景.png') no-repeat center center;
            background-size: contain;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* 分钟和秒钟数字 */
        .time-number {
            font-size: 127px;
            color: #dc3545;
            text-align: center;
            font-weight: 500;
            line-height: 1;

            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            -webkit-text-fill-color: transparent;
            /* 兼容旧版浏览器 */
            /* 定义渐变背景（可自定义颜色和方向） */
            background-image: linear-gradient(to bottom, #fff, #ffead4, #fac69b);
        }

        /* 分隔符（冒号） */
        .time-separator {
            font-size: 127px;
            color: #dc3545;
            font-weight: 500;
            line-height: 1;
        }

        /* 倒计时警告状态（最后10秒） */
        .countdown-warning .time-number,
        .countdown-warning .time-separator {
            color: #ff0000;
            animation: pulse-warning 1s infinite;
        }

        @keyframes pulse-warning {
            0% {
                transform: scale(1);
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            }

            50% {
                transform: scale(1.2);
                text-shadow: 4px 4px 12px rgba(255, 0, 0, 0.9);
            }

            100% {
                transform: scale(1);
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            }
        }

        /* 倒计时结束状态 */
        .countdown-finished .time-number,
        .countdown-finished .time-separator {
            color: #ff0000;
            animation: blink-end 0.5s infinite;
        }

        @keyframes blink-end {
            0% {
                opacity: 1;
                transform: scale(1);
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            }

            50% {
                opacity: 0.3;
                transform: scale(1.3);
                text-shadow: 5px 5px 15px rgba(255, 0, 0, 0.9);
            }

            100% {
                opacity: 1;
                transform: scale(1);
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            }
        }



        /* 右上角按钮容器 - 相对于中间背景定位 */
        .top-right-buttons {
            position: absolute;
            top: 35vh;
            right: calc(50% - 450px);
            display: flex;
            gap: 15px;
            z-index: 15;
        }

        /* 右上角按钮基础样式 */
        .top-button {
            width: 150px;
            height: 60px;
            cursor: pointer;
            transition: transform 0.2s ease, opacity 0.2s ease;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .top-button:hover {
            transform: scale(1.05);
        }

        .top-button:active {
            transform: scale(0.95);
        }

        /* 抽取中时按钮禁用状态 */
        .top-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .top-button.disabled:hover {
            transform: scale(1);
        }

        /* 暂停/继续计时按钮 - 右上角 */
        .pause-timer-btn {
            background-image: url('/static/img/index3/开始计时01.png');
        }

        .pause-timer-btn:hover {
            background-image: url('/static/img/index3/开始计时02.png');
        }

        /* 暂停状态的按钮样式 */
        .pause-timer-btn.paused {
            background-image: url('/static/img/index3/开始计时02.png');
        }

        .pause-timer-btn.paused:hover {
            background-image: url('/static/img/index3/开始计时01.png');
        }

        /* 重置时间按钮 - 右上角 */
        .reset-timer-btn {
            background-image: url('/static/img/index3/开始计时01.png');
        }

        .reset-timer-btn:hover {
            background-image: url('/static/img/index3/开始计时02.png');
        }

        /* 结束按钮 - 右上角 */
        .end-btn {
            background-image: url('/static/img/index3/重置按钮01.png');
        }

        .end-btn:hover {
            background-image: url('/static/img/index3/重置按钮02.png');
        }

        /* 版权信息模块 */
        .copyright-footer {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            color: #666666;
            font-size: 32px;
            text-align: center;
            z-index: 20;
            opacity: 0.8;
            /* text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); */
            letter-spacing: 1px;
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }

        /* 答题时间到弹框样式 */
        .time-up-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: relative;
            z-index: 1001;
        }

        .modal-background {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-bg-image {
            max-width: 90vw;
            max-height: 90vh;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        /* 关闭按钮 */
        .modal-close-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            width: 40px;
            height: 40px;
            background-color: rgba(220, 53, 69, 0.8);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .modal-close-btn:hover {
            background-color: rgba(220, 53, 69, 1);
            transform: scale(1.1);
        }



        /* 弹框动画 */
        .time-up-modal.show {
            animation: modalFadeIn 0.3s ease-out;
        }

        .time-up-modal.hide {
            animation: modalFadeOut 0.3s ease-in;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes modalFadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }

            to {
                opacity: 0;
                transform: scale(0.8);
            }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-bg-image {
                max-width: 95vw;
                max-height: 85vh;
            }

            .modal-close-btn {
                top: 15px;
                right: 20px;
                width: 35px;
                height: 35px;
                font-size: 20px;
            }
        }



        /* 全屏样式优化 */
        .fullscreen-active {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
        }

        /* 全屏状态下确保元素位置一致性 */
        body:-webkit-full-screen .main-title,
        body:-moz-full-screen .main-title,
        body:fullscreen .main-title {
            position: fixed;
        }

        body:-webkit-full-screen .start-button-container,
        body:-moz-full-screen .start-button-container,
        body:fullscreen .start-button-container {
            position: fixed;
        }

        /* 防止全屏时元素位置偏移 */
        html:-webkit-full-screen,
        html:-moz-full-screen,
        html:fullscreen {
            width: 100%;
            height: 100%;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .page2 .main-title {
                width: 65vw;
                max-width: 450px;
                top: 4vh;
            }

            .result-container {
                width: 700px;
                height: 400px;
                top: 38vh;
            }

            .result-content {
                width: 550px;
                height: 280px;
                gap: 25px;
                padding-left: 60px;
                padding-top: 30px;
            }

            .countdown-timer {
                width: 220px;
                height: 110px;
                bottom: 15px;
                right: 45px;
                gap: 15px;
            }

            .time-unit {
                width: 90px;
                height: 90px;
            }

            .time-number {
                font-size: 36px;
            }

            .time-separator {
                font-size: 32px;
                margin: 0 3px;
            }

            .result-item {
                font-size: 32px;
            }

            .result-label {
                font-size: 26px;
                min-width: 80px;
                margin-right: 10px;
            }

            .result-icon {
                width: 60px;
                height: 60px;
                margin-right: 20px;
            }

            .top-right-buttons {
                top: 33vh;
                right: calc(50% - 350px);
                gap: 12px;
            }

            .top-button {
                width: 120px;
                height: 50px;
            }

            .copyright-footer {
                font-size: 12px;
                bottom: 8px;
                letter-spacing: 0.5px;
            }
        }

        @media (max-height: 600px) {
            .page2 .main-title {
                width: 60vw;
                max-width: 400px;
                top: 3vh;
            }

            .result-container {
                width: 600px;
                height: 320px;
                top: 32vh;
            }

            .result-content {
                width: 480px;
                height: 240px;
                gap: 20px;
                padding-left: 50px;
                padding-top: 25px;
            }

            .countdown-timer {
                width: 180px;
                height: 80px;
                bottom: 10px;
                right: 35px;
                gap: 10px;
            }

            .time-unit {
                width: 70px;
                height: 70px;
            }

            .time-number {
                font-size: 28px;
            }

            .time-separator {
                font-size: 24px;
                margin: 0 2px;
            }

            .result-item {
                font-size: 28px;
            }

            .result-label {
                font-size: 22px;
                min-width: 70px;
                margin-right: 8px;
            }

            .result-icon {
                width: 50px;
                height: 50px;
                margin-right: 15px;
            }

            .top-right-buttons {
                top: 27vh;
                right: calc(50% - 250px);
                gap: 10px;
            }

            .top-button {
                width: 100px;
                height: 40px;
            }

            .copyright-footer {
                font-size: 11px;
                bottom: 5px;
                letter-spacing: 0.3px;
            }
        }

        .custom-layer {
            border-radius: 20px;
            /* width: 1434px;
            height: 671px; */
            z-index: 1;
            position: fixed;
            background-image: url('/static/img/index2/答题结束弹窗背景.png');
        }

        /* 自定义标题栏整体样式 */
        .custom-layer .layui-layer-title {
            background-color: #2c3e50;
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            padding: 12px 20px;
            border-bottom: none;
            border-radius: 6px 6px 0 0;
        }

        /* 自定义关闭按钮 */
        .custom-layer .layui-layer-setwin a {
            /* 关闭按钮大小和位置 */
            width: 30px;
            height: 30px;
            line-height: 30px;
            margin-right: 10px;
            margin-top: 5px;

            /* 关闭按钮样式 */
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 50%;
            text-align: center;
            font-size: 16px;

            /* 过渡动画 */
            transition: all 0.3s;
        }

        /* 关闭按钮 hover 效果 */
        .custom-layer .layui-layer-setwin a:hover {
            background-color: #e74c3c;
            transform: rotate(90deg);
        }

        /* 隐藏默认关闭按钮的文字（只保留X） */
        .custom-layer .layui-layer-setwin a::after {
            content: '×';
        }
    </style>
</head>

<body tabindex="0">
    <!-- 第1页 -->
    <div class="page1" id="page1">
        <img src="/static/img/index1/标题.png" class="title-img" alt="标题">
    </div>

    <!-- 第2页 -->
    <div class="page2" id="page2">
        <!-- logo -->
        <img src="/static/img/index2/logo.png" class="logo" alt="logo">
        <!-- 主标题 -->
        <img src="/static/img/index2/标题.png" class="main-title" alt="主标题">
        <!-- 中间背景和抽取结果 -->
        <img src="/static/img/index2/抽奖背景.png" class="raffle-title" alt="抽奖背景">
        <!-- 本轮标题 -->
        <img src="/static/img/index2/本轮答题标题.png" class="epicycle-title" alt="本轮标题"></img>
        <!-- 下一轮标题 -->
        <img src="/static/img/index2/下一轮答题.png" class="next-roud-title" alt="下一轮标题"></img>

        <!-- 人员信息 -->
        <div class="people-info">
            <div class="result-item">
                <div class="result-icon icon-name"></div>
                <div class="result-label">姓名：</div>
                <div class="result-text1" id="thisName">待抽取</div>
            </div>
            <div class="result-item">
                <div class="result-icon icon-department"></div>
                <div class="result-label">部门：</div>
                <div class="result-text" id="thisDept">待抽取</div>
            </div>
            <div class="result-item">
                <div class="result-icon icon-position"></div>
                <div class="result-label">职位：</div>
                <div class="result-text" id="thisPosition">待抽取</div>
            </div>
        </div>
        <!--题目 -->
        <div class="title">
            <div class="result-title">
                <div class="result-text" id="resultQuestion">
                    请点击
                    <span class="spicial-text">
                        “开始抽题”
                    </span>
                    按钮抽题
                </div>
            </div>
            <div class="this-result-title" style="display: none;">
                <span class="this-title" id="thisQuestion">如何识别房企客户的现金流风险？</span>
            </div>
        </div>
        <!-- 计时器 -->
        <div class="timer-item">
            <div class="timer-title">
                距离答题结束还剩
            </div>
            <div class="countdown-timer" id="countdownTimer">
                <div class="countdown-display" id="countdownDisplay">
                    <div class="time-unit">
                        <span class="time-number" id="minutes">10</span>
                    </div>
                    <span class="time-separator">:</span>
                    <div class="time-unit">
                        <span class="time-number" id="seconds">00</span>
                    </div>
                </div>
            </div>
            <!-- 按钮 -->
            <div class="button-items">
                <div class="start-button-container1">
                    <button type="button" class="layui-btn my-btn1">开始</button>
                </div>
                <div class="start-button-container1">
                    <button type="button" class="layui-btn my-btn2">暂停/继续</button>
                </div>
                <div class="start-button-container1">
                    <button type="button" class="layui-btn my-btn1" onclick="changeAnswer()">切换</button>
                </div>
            </div>
        </div>

        <!-- 下一轮 -->
        <div class="next-turn-item">
            <!-- 首次展示 -->
            <div class="first-show">
                <div class="next-result-item" id="resultQuestion">
                    请点击
                    <span class="spicial-text">
                        “开始抽题”
                    </span>
                    按钮抽题
                </div>
            </div>
            <!-- 抽到人之后展示 -->
            <div class="result-show" style="display: none;">
                <div class="next-result-people">
                    <span class="next-item" id="nextName">李思思</span>
                    <span class="next-item" id="nextDept">金融科技部</span>
                    <span class="next-item" id="nextPosition">科技经理</span>
                </div>
                <div class="next-result-title">
                    <span class="next-title" id="nextQuestion">如何识别房企客户的现金流风险？</span>
                </div>
            </div>
            <div class="start-button-container">
                <button type="button" class="layui-btn layui-btn-radius my-btn" id="mainActionButton"
                    onclick="startExtraction()">开始抽题</button>
            </div>
        </div>
        <!-- 中间背景和抽取结果 -->
        <!-- <div class="result-container">
            <div class="result-content">
                <div class="person-section">
                    <div class="result-item">
                        <div class="result-icon icon-name"></div>
                        <div class="result-label">姓名：</div>
                        <div class="result-text" id="resultName">准备抽取...</div>
                    </div>
                    <div class="result-item">
                        <div class="result-icon icon-department"></div>
                        <div class="result-label">部门：</div>
                        <div class="result-text" id="resultDepartment">准备抽取...</div>
                    </div>
                    <div class="result-item">
                        <div class="result-icon icon-position"></div>
                        <div class="result-label">职位：</div>
                        <div class="result-text" id="resultPosition">准备抽取...</div>
                    </div>
                </div>

                <div class="question-section">
                    <div class="result-item">
                        <div class="result-text" id="resultQuestion">准备抽取...</div>
                    </div>
                </div>
            </div>

            <div class="countdown-timer" id="countdownTimer">
                <div class="countdown-display" id="countdownDisplay">
                    <div class="time-unit">
                        <span class="time-number" id="minutes">10</span>
                    </div>
                    <span class="time-separator">:</span>
                    <div class="time-unit">
                        <span class="time-number" id="seconds">00</span>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- 右上角按钮 - 相对于中间背景定位 -->
        <!-- <div class="top-right-buttons">
            <div class="top-button pause-timer-btn" id="pauseTimerBtn" onclick="toggleTimer()"></div>
            <div class="top-button reset-timer-btn" onclick="resetTimer()"></div>
            <div class="top-button end-btn" onclick="endSession()"></div>
        </div> -->

        <!-- 版权信息 -->
        <div class="copyright-footer">
            金融科技部版权所有
        </div>
    </div>

    <script>
        // 本次抽取次数
        let currentTimes = 1;
        let currentPage = 1;
        let isExtracting = false;
        let countdownInterval = null;
        let totalSeconds = 600; // 10分钟 = 600秒
        let isCountdownRunning = false;
        let isCountdownPaused = false;
        let extractionInterval = null;
        let extractionSoundInterval = null;
        let extractionTimeout = null; // 用于跟踪6秒定时器
        let allPersons = [];
        let allQuestions = [];
        let extractionStartTime = null;
        let dialogClose = true;
        let timeOverText = `<div style="position: absolute;">
                                            <div style="position: absolute;top: 133px;left: 600px;width: 222px;height: 229px;background-image: url('/static/img/index2/插画.png')">
                                            </div>
                                            <div style="position: absolute;top: 362px;left: 571px;width: 303px;height: 127px;font-size: 75px;font-weight: 500;">
                                                答题结束
                                            </div>
                                            <div style="position: absolute;top: 500px;left: 571px;width: 303px;height: 127px;font-size: 75px;font-weight: 500;">
                                                <button type="button" class="layui-btn my-btn3" onclick="closeDialog()">关闭</button>
                                            </div>
                                        </div>`
        // 页面加载完成后设置焦点和全屏，以便接收键盘事件
        window.onload = function () {
            document.body.focus();
            requestFullscreen();
            checkTaskStatus();
        };
        // 1. 初始化Layui并暴露layer到全局
        let layer;
        layui.use('layer', function () {
            layer = layui.layer; // 将layer对象全局化
        });
        // 调用自定义页面层
        function openCustomLayer(customContent) {
            // 确保layer已加载
            if (!layer) {
                alert('Layui模块加载中，请稍后');
                return;
            }

            // 调用页面层
            layer.open({
                type: 1,
                title: ''
                ,
                // 添加自定义类名到弹框容器
                skin: 'custom-layer',
                shadeClose: false,
                closeBtn: 0,
                offset: ['245px', '881px'],
                area: ['1422px', '671px'],
                content: customContent,
                // 隐藏默认最大化/最小化按钮（只保留关闭按钮）
                maxmin: false
            });
            dialogClose = false;
        }

        // 关闭弹窗
        function closeDialog() {
            dialogClose = true;
            layer.close(layer.index);
        }

        // 检查任务状态
        async function checkTaskStatus() {
            try {
                const quotaCheck = await checkQuotaAvailable();
                if (!quotaCheck.available) {
                    let customContent = `<div style="position: absolute;">
                                            <div style="position: absolute;top: 298px;left: 339px;width: 703px;height: 127px;font-size: 61px;font-weight: 500;text-align: center;">
                                                <span>${quotaCheck.message}</span>
                                            </div>
                                        </div>`
                    openCustomLayer(customContent)
                    if (!quotaCheck.available) {
                        return;
                    }
                    return;
                }
            } catch (error) {
                console.warn('任务状态检查失败：', error);
            }
        }

        function changeAnswer() {
            let modals1 = document.getElementsByClassName('result-title')
            modals1[0].style.display = 'none'
            let modals2 = document.getElementsByClassName('result-content')
            modals1[0].style.display = 'block'


        }

        // 请求全屏
        function requestFullscreen() {
            const docElm = document.documentElement;
            if (docElm.requestFullscreen) {
                docElm.requestFullscreen();
            } else if (docElm.mozRequestFullScreen) {
                docElm.mozRequestFullScreen();
            } else if (docElm.webkitRequestFullScreen) {
                docElm.webkitRequestFullScreen();
            } else if (docElm.msRequestFullscreen) {
                docElm.msRequestFullscreen();
            }
        }

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);

        function handleFullscreenChange() {
            setTimeout(function () {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        }

        // 全局键盘事件监听
        document.addEventListener('keydown', function (event) {
            if (dialogClose == false) {
                return;
            }
            if (currentPage === 1 && (event.code === 'Space' || event.code === 'Enter')) {
                event.preventDefault();
                switchTopage2();
            } else if (currentPage === 3 && event.code === 'Space') {
                event.preventDefault();
                returnToPage1Frompage2();
            }
        });

        // 空格键监听器（用于结束会话后回到页面1）
        let spaceKeyListenerEnabled = false;

        function enableSpaceKeyListener() {
            spaceKeyListenerEnabled = true;
        }

        function disableSpaceKeyListener() {
            spaceKeyListenerEnabled = false;
        }

        // 添加专门的空格键监听
        document.addEventListener('keydown', function (event) {
            if (spaceKeyListenerEnabled && event.code === 'Space') {
                event.preventDefault();
                returnToPage1();
            }
        });

        // 添加ESC键关闭弹框的监听
        document.addEventListener('keydown', function (event) {
            if (event.code === 'Escape') {
                const modal = document.getElementById('timeUpModal');
                if (modal && modal.style.display === 'flex') {
                    event.preventDefault();
                    closeTimeUpModal();
                }
            }
        });

        // 从页面2回到页面1的函数
        function returnToPage1Frompage2() {
            // 检查是否正在抽取中
            if (isExtracting) {
                return;
            }

            // 检查是否正在倒计时且计时器没有归零
            if (isCountdownRunning && totalSeconds > 0) {
                return;
            }

            // 检查是否倒计时暂停但时间未归零
            if (isCountdownPaused && totalSeconds > 0) {
                return;
            }

            // 重置所有状态
            resetAllStates();

            // 回到第一页
            currentPage = 1;
            document.getElementById('page2').classList.remove('active');
            document.getElementById('page1').style.display = 'flex';

            // 恢复背景和点击事件
            document.body.style.backgroundImage = "url('/static/img/index1/背景.jpg')";
            document.body.onclick = switchTopage2;
            document.body.style.cursor = 'pointer';
        }

        // 回到页面1的函数（用于结束会话后的空格键监听）
        function returnToPage1() {
            // 禁用空格键监听
            disableSpaceKeyListener();

            // 重置所有状态
            resetAllStates();

            // 回到第一页
            currentPage = 1;
            document.getElementById('page2').classList.remove('active');
            document.getElementById('page1').style.display = 'flex';

            // 恢复背景和点击事件
            document.body.style.backgroundImage = "url('/static/img/index1/背景.jpg')";
            document.body.onclick = switchTopage2;
            document.body.style.cursor = 'pointer';
        }

        // 重置所有状态的函数
        function resetAllStates() {
            // 重置抽取状态
            isExtracting = false;

            // 停止所有定时器
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 停止抽题音效循环
            stopExtractionSoundLoop();

            // 重置倒计时状态
            isCountdownRunning = false;
            isCountdownPaused = false;
            totalSeconds = 600;

            // 重置显示内容
            document.getElementById('resultName').textContent = '准备抽取...';
            document.getElementById('resultDepartment').textContent = '准备抽取...';
            document.getElementById('resultPosition').textContent = '准备抽取...';
            document.getElementById('resultQuestion').textContent = '准备抽取...';

            // 重置倒计时显示
            updateCountdownDisplay();

            // 重置倒计时样式
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 重置结果项样式
            const resultTexts = document.querySelectorAll('.result-text');
            resultTexts.forEach(text => {
                text.style.color = '#2c3e50';
                text.style.transform = 'scale(1)';
                text.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
            });

            // 移除滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => {
                item.classList.remove('rolling');
            });

            // 重新启用所有按钮
            enableTimerButtons();
        }

        // 直接切换到第2页
        function switchTopage2() {
            if (currentPage !== 1) return;

            currentPage = 3;

            // 切换背景图片到页面2
            document.body.style.backgroundImage = "url('/static/img/index2/背景.jpg')";
            document.body.style.backgroundSize = "cover";
            document.body.style.backgroundPosition = "center center";
            document.body.style.backgroundRepeat = "no-repeat";

            // 隐藏第一页，直接显示第2页
            document.getElementById('page1').style.display = 'none';
            document.getElementById('page2').classList.add('active');

            // 移除body的点击事件
            document.body.onclick = null;
            document.body.style.cursor = 'default';

            // 显示倒计时器
            const countdownTimer = document.getElementById('countdownTimer');
            if (countdownTimer) {
                countdownTimer.style.display = 'flex';
            }

            // 初始化倒计时显示
            if (!isCountdownRunning && !isCountdownPaused) {
                totalSeconds = 600;
                updateCountdownDisplay();
            }

            // 设置按钮为开始抽题状态
            setupStartExtractionButton();

            // 强制重新计算布局以确保全屏兼容性
            setTimeout(function () {
                window.dispatchEvent(new Event('resize'));
            }, 50);
        }

        // 设置按钮为开始抽题状态
        function setupStartExtractionButton() {
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.remove('restart-mode', 'extracting');
                mainButton.onclick = function (event) {
                    startExtraction(event);
                };
            }
        }

        // 设置按钮为重新抽题状态
        function setupRestartExtractionButton() {
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.add('restart-mode');
                mainButton.classList.remove('extracting');
                mainButton.onclick = function (event) {
                    restartExtraction(event);
                };
            }
        }

        // 禁用计时器和结束按钮
        function disableTimerButtons() {
            const pauseBtn = document.getElementById('pauseTimerBtn');
            const resetBtn = document.querySelector('.reset-timer-btn');
            const endBtn = document.querySelector('.end-btn');

            if (pauseBtn) {
                pauseBtn.classList.add('disabled');
            }
            if (resetBtn) {
                resetBtn.classList.add('disabled');
            }
            if (endBtn) {
                endBtn.classList.add('disabled');
            }
        }

        // 启用计时器和结束按钮
        function enableTimerButtons() {
            const pauseBtn = document.getElementById('pauseTimerBtn');
            const resetBtn = document.querySelector('.reset-timer-btn');
            const endBtn = document.querySelector('.end-btn');

            if (pauseBtn) {
                pauseBtn.classList.remove('disabled');
            }
            if (resetBtn) {
                resetBtn.classList.remove('disabled');
            }
            if (endBtn) {
                endBtn.classList.remove('disabled');
            }
        }

        // 校验当天是否配置任务以及名额是否充足
        async function checkQuotaAvailable() {
            try {
                const response = await fetch('/extract/check');

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('名额校验失败，HTTP状态:', response.status);
                    console.error('错误详情:', errorData);
                    throw new Error(errorData.detail || '名额校验失败');
                }

                const result = await response.json();

                if (!result.available) {
                    return { available: false, message: result.message };
                }

                return { available: true, message: null };

            } catch (error) {
                console.error('名额校验异常:', error);
                return {
                    available: false,
                    message: '名额校验失败：' + error.message
                };
            }
        }

        // 获取抽取数据（用于滚动显示）
        async function fetchExtractionData() {
            try {
                const response = await fetch('/extract/data');

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('获取数据失败:', errorData);
                    throw new Error(errorData.detail || '获取数据失败');
                }

                const data = await response.json();

                return {
                    success: true,
                    data: data
                };

            } catch (error) {
                console.error('获取数据异常:', error);
                return {
                    success: false,
                    message: '获取数据失败：' + error.message
                };
            }
        }

        // 开始抽取功能
        async function startExtraction(event) {
            // event.stopPropagation();

            if (isExtracting) return; // 防止重复点击

            // 先校验名额
            const quotaCheck = await checkQuotaAvailable();
            if (!quotaCheck.available) {
                let customContent = `<div style="position: absolute;">
                                            <div style="position: absolute;top: 298px;left: 339px;width: 703px;height: 127px;font-size: 61px;font-weight: 500;text-align: center;">
                                                <span>${quotaCheck.message}</span>
                                            </div>
                                            <div style="position: absolute;top: 500px;left: 571px;width: 303px;height: 127px;font-size: 75px;font-weight: 500;">
                                                 <button type="button" class="layui-btn my-btn3" onclick="closeDialog()">关闭</button>
                                             </div>
                                        </div>`
                openCustomLayer(customContent)
                if (!quotaCheck.available) {
                    return;
                }
                return;
            }


            // 获取抽取数据
            const dataResult = await fetchExtractionData();
            if (!dataResult.success) {
                alert(dataResult.message);
                return;
            }

            // 名额充足且数据获取成功，开始抽取流程
            isExtracting = true;
            extractionStartTime = Date.now();

            // 切换按钮状态为抽取中
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.add('extracting');
                mainButton.onclick = null;
            }

            // 禁用计时器相关按钮
            disableTimerButtons();

            // 使用获取的数据
            allPersons = dataResult.data.persons;
            allQuestions = dataResult.data.questions;

            if (currentTimes == 1) {
                // 去掉 请开始抽题“开始抽题”按钮抽题 模块的覆盖
                let models1 = document.getElementsByClassName('first-show')
                models1[0].style.display = 'none'

                let models2 = document.getElementsByClassName('result-show')
                models2[0].style.display = 'block'
            }

            // 开始播放抽题音效
            startExtractionSoundLoop();

            // 开始滚动显示
            startRollingDisplay();

            // 6秒后停止滚动并获取最终结果
            extractionTimeout = setTimeout(async () => {
                stopRollingAndShowResult();
            }, 6000);
        }



        // 开始滚动显示
        function startRollingDisplay() {
            // 添加滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => item.classList.add('rolling'));

            // 开始人员信息和题目滚动
            extractionInterval = setInterval(() => {
                const randomPerson = allPersons[Math.floor(Math.random() * allPersons.length)];
                const randomQuestion = allQuestions[Math.floor(Math.random() * allQuestions.length)];

                // 更新人员信息显示
                document.getElementById('nextName').textContent = randomPerson.name;
                document.getElementById('nextDept').textContent = randomPerson.department;
                document.getElementById('nextPosition').textContent = randomPerson.position;

                // 更新题目信息显示
                document.getElementById('nextQuestion').textContent = randomQuestion.title;

            }, 80); // 每80ms更新一次，营造快速滚动效果
        }

        // 停止滚动并显示最终结果
        async function stopRollingAndShowResult() {
            // 立即停止抽题音效循环
            stopExtractionSoundLoop();

            // 停止滚动
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }

            // 移除滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => item.classList.remove('rolling'));

            try {
                // 调用后端API获取最终抽取结果
                const response = await fetch('/extract', { method: 'POST' });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '抽取失败');
                }

                const result = await response.json();

                // 显示最终结果
                document.getElementById('nextName').textContent = result.person.name;
                document.getElementById('nextDept').textContent = result.person.department;
                document.getElementById('nextPosition').textContent = result.person.position;
                document.getElementById('nextQuestion').textContent = result.question.title;

                // 添加结果确定的视觉效果 - 只应用于文字值部分
                // setTimeout(() => {
                //     const resultTexts = document.querySelectorAll('.result-text');
                //     resultTexts.forEach(text => {
                //         text.style.color = '#dc3545';
                //         text.style.transform = 'scale(1.05)';
                //         text.style.textShadow = '3px 3px 8px rgba(220,53,69,0.5)';
                //     });
                // }, 100);

                // 更新按钮状态为"重新抽题"
                updateButtonToRestart();

                // 重新启用计时器相关按钮
                enableTimerButtons();

            } catch (error) {
                console.error('获取最终结果失败：', error);
                alert('抽取失败：' + error.message);
                resetExtractionState();
                // 发生错误时，重置按钮为开始抽题状态
                setupStartExtractionButton();
                // 重新启用计时器相关按钮
                enableTimerButtons();
            }
        }



        // 更新按钮状态为重新抽取
        function updateButtonToRestart() {
            // 设置按钮为重新抽题状态
            setupRestartExtractionButton();

            // 重置抽取状态
            isExtracting = false;
        }

        // 重置抽取状态
        function resetExtractionState() {
            isExtracting = false;

            // 停止滚动
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }

            // 清理6秒定时器
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 停止抽题音效循环
            stopExtractionSoundLoop();

            // 移除滚动效果样式
            const resultItems = document.querySelectorAll('.result-show');
            resultItems.forEach(item => {
                item.classList.remove('rolling');
            });

            // 重置文字值的样式
            const resultTexts = document.querySelectorAll('.result-text');
            resultTexts.forEach(text => {
                text.style.color = '#2c3e50';
                text.style.transform = 'scale(1)';
                text.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
            });

            // 如果在第2页，设置按钮为开始抽题状态
            if (currentPage === 3) {
                setupStartExtractionButton();
            }

            // 重新启用计时器相关按钮
            enableTimerButtons();
        }

        // 暂停/继续计时功能
        function toggleTimer() {
            // 检查是否正在抽取中，如果是则不允许操作计时器
            if (isExtracting) {
                return;
            }

            if (!isCountdownRunning && !isCountdownPaused) {
                // 首次启动倒计时
                startCountdown();
            } else if (isCountdownRunning && !isCountdownPaused) {
                // 暂停倒计时
                pauseCountdown();
            } else if (isCountdownPaused) {
                // 继续倒计时
                resumeCountdown();
            }
        }

        // 启动倒计时
        function startCountdown() {
            // 重置倒计时（测试用15秒）
            totalSeconds = 15; // 测试用15秒，正式版改为600
            isCountdownRunning = true;
            isCountdownPaused = false;

            // 清除之前的状态
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 立即更新显示
            updateCountdownDisplay();

            // 更新按钮状态
            updateTimerButtonState();

            // 开始倒计时
            runCountdown();
        }

        // 暂停倒计时
        function pauseCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            isCountdownPaused = true;
            updateTimerButtonState();
        }

        // 继续倒计时
        function resumeCountdown() {
            isCountdownPaused = false;
            updateTimerButtonState();

            // 继续倒计时
            runCountdown();
        }

        // 运行倒计时逻辑
        function runCountdown() {
            countdownInterval = setInterval(function () {
                totalSeconds--;
                updateCountdownDisplay();



                // 最后10秒警告
                if (totalSeconds <= 10 && totalSeconds > 0) {
                    document.getElementById('countdownDisplay').classList.add('countdown-warning');
                    // 播放提示音（每秒一次）
                    playBeep();
                }

                // 时间到
                if (totalSeconds <= 0) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                    isCountdownRunning = false;
                    isCountdownPaused = false;

                    // 更新显示为00:00
                    updateCountdownDisplay();

                    // 清除所有动画状态，时间为0时不显示动画效果
                    document.getElementById('countdownDisplay').classList.remove('countdown-warning', 'countdown-finished');

                    // 更新按钮状态
                    updateTimerButtonState();

                    // 播放结束音
                    playEndSound();

                    // 显示答题时间到弹框
                    showTimeUpModal();
                }
            }, 1000);
        }

        // 更新计时按钮状态
        function updateTimerButtonState() {
            const pauseBtn = document.getElementById('pauseTimerBtn');

            if (isCountdownPaused) {
                // 暂停状态：显示继续图标
                pauseBtn.classList.add('paused');
            } else {
                // 运行状态：显示暂停图标
                pauseBtn.classList.remove('paused');
            }
        }

        // 重置时间功能（只重置倒计时）
        function resetTimer() {
            // 检查是否正在抽取中，如果是则不允许重置计时器
            if (isExtracting) {
                return;
            }

            // 停止当前倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            // 重置倒计时状态
            isCountdownRunning = false;
            isCountdownPaused = false;
            totalSeconds = 600; // 重置为10分钟

            // 清除倒计时样式
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 更新显示
            updateCountdownDisplay();

            // 更新按钮状态
            updateTimerButtonState();
        }

        // 更新倒计时显示
        function updateCountdownDisplay() {
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;

            const minutesElement = document.getElementById('minutes');
            const secondsElement = document.getElementById('seconds');

            if (minutesElement && secondsElement) {
                minutesElement.textContent = minutes.toString().padStart(2, '0');
                secondsElement.textContent = seconds.toString().padStart(2, '0');
            } else {
                console.error('找不到倒计时显示元素');
            }
        }

        // 播放提示音
        function playBeep() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                console.error('播放提示音失败:', e);
            }
        }

        // 播放结束音
        function playEndSound() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.5, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 1);
            } catch (e) {
                console.error('播放结束音失败:', e);
            }
        }

        // 抽题音效：固定使用铃铛和弦音效

        // 全局AudioContext实例，避免重复创建
        let globalAudioContext = null;

        // 获取或创建AudioContext
        function getAudioContext() {
            if (!globalAudioContext || globalAudioContext.state === 'closed') {
                try {
                    globalAudioContext = new (window.AudioContext || window.webkitAudioContext)();
                } catch (error) {
                    console.error('创建AudioContext失败:', error);
                    return null;
                }
            }

            // 如果AudioContext被暂停，尝试恢复
            if (globalAudioContext.state === 'suspended') {
                globalAudioContext.resume().catch(error => {
                    console.error('恢复AudioContext失败:', error);
                });
            }

            return globalAudioContext;
        }

        // 播放抽题提示音（铃铛和弦音效）
        function playExtractionSound() {
            try {
                playBellChimeSound();
            } catch (e) {
                console.error('播放抽题提示音失败:', e);
            }
        }



        // 音效4：铃铛和弦音效（选人选题专用优化版）
        function playBellChimeSound() {
            const audioContext = getAudioContext();
            if (!audioContext) return;

            try {
                const oscillator1 = audioContext.createOscillator();
                const oscillator2 = audioContext.createOscillator();
                const oscillator3 = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator1.connect(gainNode);
                oscillator2.connect(gainNode);
                oscillator3.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // 优化的三音和弦，更适合选人选题场景
                oscillator1.type = 'sine';
                oscillator2.type = 'sine';
                oscillator3.type = 'sine';

                // C大调三和弦：C5 + E5 + G5，营造正面、积极的氛围
                oscillator1.frequency.setValueAtTime(523, audioContext.currentTime); // C5 - 主音
                oscillator2.frequency.setValueAtTime(659, audioContext.currentTime); // E5 - 三音
                oscillator3.frequency.setValueAtTime(784, audioContext.currentTime); // G5 - 五音

                // 温和的音量包络，适合重复播放
                gainNode.gain.setValueAtTime(0.12, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.25);

                oscillator1.start(audioContext.currentTime);
                oscillator2.start(audioContext.currentTime);
                oscillator3.start(audioContext.currentTime);
                oscillator1.stop(audioContext.currentTime + 0.25);
                oscillator2.stop(audioContext.currentTime + 0.25);
                oscillator3.stop(audioContext.currentTime + 0.25);

            } catch (error) {
                console.error('播放铃铛和弦音效失败:', error);
            }
        }





        // 开始抽题音效循环播放
        function startExtractionSoundLoop() {
            // 确保先停止之前的循环
            if (extractionSoundInterval) {
                clearInterval(extractionSoundInterval);
                extractionSoundInterval = null;
            }

            // 检查是否还在抽取状态
            if (!isExtracting) {
                return;
            }

            // 立即播放一次
            playExtractionSound();

            // 每0.25秒播放一次（推荐频率）
            extractionSoundInterval = setInterval(() => {
                // 检查是否还在抽取状态
                if (!isExtracting) {
                    stopExtractionSoundLoop();
                    return;
                }
                playExtractionSound();
            }, 250);
        }

        // 停止抽题音效循环播放
        function stopExtractionSoundLoop() {
            if (extractionSoundInterval) {
                clearInterval(extractionSoundInterval);
                extractionSoundInterval = null;
            }
        }

        // 重新抽题功能
        async function restartExtraction(event) {
            event.stopPropagation();

            // 检查是否正在抽取中
            if (isExtracting) {
                return;
            }

            // 先校验名额
            const quotaCheck = await checkQuotaAvailable();
            if (!quotaCheck.available) {
                alert(quotaCheck.message);
                return;
            }

            // 获取抽取数据
            const dataResult = await fetchExtractionData();
            if (!dataResult.success) {
                alert(dataResult.message);
                return;
            }

            // 确保停止之前的所有定时器和音效
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }

            // 清理之前的6秒定时器
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
                console.log('清理之前的6秒定时器');
            }

            // 确保停止之前的音效循环
            stopExtractionSoundLoop();

            // 名额充足且数据获取成功，开始新的抽取流程
            isExtracting = true;
            extractionStartTime = Date.now();

            // 设置按钮为抽取中状态
            const mainButton = document.getElementById('mainActionButton');
            if (mainButton) {
                mainButton.classList.add('extracting');
                mainButton.onclick = null;
            }

            // 禁用计时器相关按钮
            disableTimerButtons();

            // 重置结果项样式 - 只重置文字值部分
            const resultTexts = document.querySelectorAll('.result-text');
            resultTexts.forEach(text => {
                text.style.color = '#2c3e50';
                text.style.transform = 'scale(1)';
                text.style.textShadow = '2px 2px 4px rgba(0,0,0,0.3)';
            });

            // 重置显示内容
            document.getElementById('nextName').textContent = '正在抽取...';
            document.getElementById('nextDept').textContent = '正在抽取...';
            document.getElementById('nextPosition').textContent = '正在抽取...';
            document.getElementById('nextQuestion').textContent = '正在抽取...';

            // 使用获取的数据
            allPersons = dataResult.data.persons;
            allQuestions = dataResult.data.questions;

            // 开始播放抽题音效
            startExtractionSoundLoop();

            // 开始滚动显示
            startRollingDisplay();

            // 6秒后停止滚动并获取最终结果
            extractionTimeout = setTimeout(async () => {
                stopRollingAndShowResult();
            }, 6000);
        }

        // 结束会话功能（将计时器置为0，等待空格键回到页面1）
        function endSession() {
            // 检查是否正在抽取中，如果是则不允许结束会话
            if (isExtracting) {
                return;
            }

            // 停止所有定时器
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            if (extractionInterval) {
                clearInterval(extractionInterval);
                extractionInterval = null;
            }
            if (extractionTimeout) {
                clearTimeout(extractionTimeout);
                extractionTimeout = null;
            }

            // 停止抽题音效循环
            stopExtractionSoundLoop();

            // 设置倒计时为0并显示
            totalSeconds = 0;
            isCountdownRunning = false;
            isCountdownPaused = false;

            // 更新显示为00:00
            updateCountdownDisplay();

            // 清除所有动画状态样式，不添加结束状态动画
            const countdownDisplay = document.getElementById('countdownDisplay');
            if (countdownDisplay) {
                countdownDisplay.classList.remove('countdown-warning', 'countdown-finished');
            }

            // 更新按钮状态
            updateTimerButtonState();

            // 启用空格键监听，等待用户按空格键回到页面1
            enableSpaceKeyListener();
        }

        // 显示答题时间到弹框
        function showTimeUpModal() {
            console.log('显示答题时间到弹框');

            const modal = document.getElementById('timeUpModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('show');
                modal.classList.remove('hide');

                // 禁用页面滚动
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭答题时间到弹框
        function closeTimeUpModal() {
            console.log('关闭答题时间到弹框');

            const modal = document.getElementById('timeUpModal');
            if (modal) {
                modal.classList.add('hide');
                modal.classList.remove('show');

                // 动画结束后隐藏弹框
                setTimeout(() => {
                    modal.style.display = 'none';
                    // 恢复页面滚动
                    document.body.style.overflow = 'hidden'; // 保持hidden因为是全屏应用
                }, 300);
            }
        }


    </script>
</body>

</html>